import React from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Link, useNavigate } from '@tanstack/react-router'
import { logout } from '../store/slices/auth.slice'
import AxiosUtil from '../utils/axios.util'
import { FaUser, FaSignInAlt, FaSignOutAlt } from 'react-icons/fa'

const Navbar = () => {
  const { isAuthenticated, user } = useSelector(state => state.auth)
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const handleLogout = async () => {
    try {
      await AxiosUtil.get('/auth/logout')
      dispatch(logout())
      navigate({ to: '/' })
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-dark2/95 backdrop-blur-sm border-b border-dark3/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center group">
            <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent group-hover:from-blue-300 group-hover:to-blue-500 transition-all duration-300">
              URL Shortener
            </span>
          </Link>

          {/* User Section */}
          <div className="flex items-center space-x-3">
            {isAuthenticated ? (
              <div className="flex items-center space-x-3">
                {/* User Info */}
                <div className="flex items-center space-x-3 bg-dark3/50 px-3 py-2 rounded-lg">
                  <div className="w-9 h-9 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                    <FaUser className="text-white text-sm" />
                  </div>
                  <span className="text-white text-sm font-medium hidden sm:block">
                    {user?.username || user?.email?.split('@')[0]}
                  </span>
                </div>

                {/* Dashboard Link */}
                <Link
                  to="/dashboard"
                  className="px-4 py-2 text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 rounded-lg transition-all duration-200 text-sm font-medium"
                >
                  Dashboard
                </Link>

                {/* Logout Button */}
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-2 px-4 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-200 text-sm font-medium"
                >
                  <FaSignOutAlt className="text-xs" />
                  <span className="hidden sm:block">Logout</span>
                </button>
              </div>
            ) : (
              <Link
                to="/auth"
                className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-2.5 rounded-lg transition-all duration-200 shadow-lg hover:shadow-blue-500/25 font-medium"
              >
                <FaSignInAlt className="text-sm" />
                <span>Login</span>
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
