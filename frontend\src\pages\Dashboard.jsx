
import React from 'react'
import { useSelector } from 'react-redux'
import GridBackground from '../components/GridBackground'

const Dashboard = () => {
  const { user } = useSelector(state => state.auth)

  return (
    <main className="dark px-4 pt-20 bg-gradient-bg text-white flex flex-col justify-start items-center min-h-screen w-screen">
      <GridBackground />

      <div className="headings z-20 relative w-full flex flex-col justify-between items-center mb-8">
        <h1 className="text-[2.5rem] sm:text-[4rem] lg:text-[4rem] leading-none">
          Welcome, {user?.username || user?.email}!
        </h1>
        <h2 className="text-[1.5rem] sm:text-[2rem] lg:text-[2rem] leading-none text-blue-400 mt-2">
          Your URL Dashboard
        </h2>
      </div>

      <div className="dashboard-content z-20 bg-dark2 relative sm:gap-4 gap-4 flex flex-col w-[95%] lg:w-[70%] rounded-sm p-6">
        <div className="stats-section">
          <h3 className="text-xl font-semibold mb-4">Your Statistics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="stat-card bg-dark3 p-4 rounded-sm">
              <h4 className="text-lg font-medium text-blue-400">Total URLs</h4>
              <p className="text-2xl font-bold">0</p>
            </div>
            <div className="stat-card bg-dark3 p-4 rounded-sm">
              <h4 className="text-lg font-medium text-green-400">Total Clicks</h4>
              <p className="text-2xl font-bold">0</p>
            </div>
            <div className="stat-card bg-dark3 p-4 rounded-sm">
              <h4 className="text-lg font-medium text-purple-400">Custom URLs</h4>
              <p className="text-2xl font-bold">0</p>
            </div>
          </div>
        </div>

        <div className="recent-urls-section mt-6">
          <h3 className="text-xl font-semibold mb-4">Recent URLs</h3>
          <div className="bg-dark3 p-4 rounded-sm">
            <p className="text-gray-400">No URLs created yet. Start shortening some links!</p>
          </div>
        </div>
      </div>
    </main>
  )
}

export default Dashboard