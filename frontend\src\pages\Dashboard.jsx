
import React from 'react'
import { useSelector } from 'react-redux'
import GridBackground from '../components/GridBackground'

const Dashboard = () => {
  const { user } = useSelector(state => state.auth)

  return (
    <main className="dark px-4 pt-20 bg-gradient-bg text-white flex flex-col justify-start items-center min-h-screen w-screen">
      <GridBackground />

      <div className="headings z-20 relative w-full flex flex-col justify-between items-center mb-8">
        <h1 className="text-[2.5rem] sm:text-[4rem] lg:text-[4rem] leading-none text-center">
          Welcome back, {user?.username || user?.email?.split('@')[0]}! 👋
        </h1>
        <h2 className="text-[1.2rem] sm:text-[1.5rem] lg:text-[1.5rem] leading-none text-blue-400 mt-4 text-center">
          📊 Your URL Dashboard & Analytics
        </h2>
      </div>

      <div className="dashboard-content z-20 bg-dark2/80 backdrop-blur-sm relative gap-6 flex flex-col w-[95%] lg:w-[80%] rounded-xl p-8 shadow-2xl border border-dark3/30">
        <div className="stats-section">
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
            <span>📈</span>
            Your Statistics
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="stat-card bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-500/30 p-6 rounded-xl hover:scale-105 transition-transform duration-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-semibold text-blue-400">Total URLs</h4>
                <span className="text-2xl">🔗</span>
              </div>
              <p className="text-3xl font-bold text-white">0</p>
              <p className="text-sm text-gray-400 mt-1">Links created</p>
            </div>
            <div className="stat-card bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-500/30 p-6 rounded-xl hover:scale-105 transition-transform duration-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-semibold text-green-400">Total Clicks</h4>
                <span className="text-2xl">👆</span>
              </div>
              <p className="text-3xl font-bold text-white">0</p>
              <p className="text-sm text-gray-400 mt-1">Times clicked</p>
            </div>
            <div className="stat-card bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-500/30 p-6 rounded-xl hover:scale-105 transition-transform duration-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-semibold text-purple-400">Custom URLs</h4>
                <span className="text-2xl">✨</span>
              </div>
              <p className="text-3xl font-bold text-white">0</p>
              <p className="text-sm text-gray-400 mt-1">Custom links</p>
            </div>
          </div>
        </div>

        <div className="recent-urls-section">
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
            <span>🕒</span>
            Recent URLs
          </h3>
          <div className="bg-dark3/50 border border-dark3/50 p-8 rounded-xl text-center">
            <div className="flex flex-col items-center gap-4">
              <span className="text-6xl">🔗</span>
              <p className="text-gray-400 text-lg">No URLs created yet</p>
              <p className="text-gray-500 text-sm">Start shortening some links to see them here!</p>
              <button className="mt-4 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-blue-500/25">
                Create Your First Link
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}

export default Dashboard