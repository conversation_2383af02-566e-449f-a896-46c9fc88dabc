
import React from 'react'
import { useSelector } from 'react-redux'
import GridBackground from '../components/GridBackground'

const Dashboard = () => {
  const { user } = useSelector(state => state.auth)

  return (
    <main className="dark px-4 pt-20 bg-gradient-bg text-white flex flex-col justify-start items-center min-h-screen w-screen">
      <GridBackground />

      <div className="headings z-20 relative w-full flex flex-col justify-between items-center mb-8">
        <h1 className="text-[2.5rem] sm:text-[4rem] lg:text-[4rem] leading-none text-center">
          Welcome back, {user?.username || user?.email?.split('@')[0]}! 👋
        </h1>
        <h2 className="text-[1.2rem] opacity-60 sm:text-[1.5rem] lg:text-[1.5rem] leading-none text-blue-480 mt-4 text-center">
          📊 Your URL Dashboard & Analytics
        </h2>
      </div>

      <div className="dashboard-content z-20 relative gap-6 flex flex-col w-[95%] lg:w-[80%] rounded-xl p-4 shadow-2xl">
        <div className="stats-section">
          <h3 className="text-2xl font-bold mb-12 relative block flex items-center gap-2">
            <span>📈</span>
            Your Statistics
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div style={{padding:10}} className="stat-card bg-gradient-to-br from-blue-500/50 to-blue-600/50 border border-blue-500/30 p-8 rounded-xl  transition-transform duration-200">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-white">Total URLs</h4>
                <span className="text-3xl">🔗</span>
              </div>
              <p className="text-4xl font-bold text-white mb-2">0</p>
              <p className="text-sm text-white">Links created</p>
            </div>
            <div style={{padding:10}} className="stat-card bg-gradient-to-br from-green-500/50 to-green-600/50 border border-green-500/30 p-8 rounded-xl  transition-transform duration-200">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-white">Total Clicks</h4>
                <span className="text-3xl">👆</span>
              </div>
              <p className="text-4xl font-bold text-white mb-2">0</p>
              <p className="text-sm text-white">Times clicked</p>
            </div>
            <div style={{padding:10}} className="stat-card bg-gradient-to-br from-purple-500/50 to-purple-600/50 border border-purple-500/30 p-8 rounded-xl  transition-transform duration-200">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-white">Custom URLs</h4>
                <span className="text-3xl">✨</span>
              </div>
              <p className="text-4xl font-bold text-white mb-2">0</p>
              <p className="text-sm text-white">Custom links</p>
            </div>
          </div>
        </div>

        <div className="recent-urls-section py-4">
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
            <span>🕒</span>
            Recent URLs
          </h3>
          <div className="bg-dark3/50 border border-dark3/50 p-8 rounded-xl text-center">
            <div className="flex flex-col items-center g">
              <span className="text-6xl">🔗</span>
              <p className="text-gray-4008text-lg">No URLs created yet</p>
              <p className="text-gray-500 text-sm">Start shortening some links to see them here!</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}

export default Dashboard