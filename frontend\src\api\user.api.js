import { login } from "../store/slices/auth.slice"
import AxiosInstance from "../utils/axios.util"
import {redirect} from '@tanstack/react-router'



export const GetUser = async () => {
    const User = await AxiosInstance.get('/auth/me')
    return User
}


export const IsAuthenticated = async ({context}) => {
    try {
        const {queryClient,AuthStore} = context 
    const Auth = AuthStore.getState().auth

    const User = await queryClient.ensureQueryData({
        queryKey:['current-user'],
        queryFn:GetUser
    })

    console.log(User)

    AuthStore.dispatch(login(User.data.user))

    return true; 
    } catch (error) {
        console.log(error)
        return redirect({to:'/auth'})
    }

}