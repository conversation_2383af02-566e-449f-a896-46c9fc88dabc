import AxiosInstance from "../utils/axios.util"
import {redirect} from '@tanstack/react-router'



export const GetUser = async () => {
    const User = await AxiosInstance.get('/api/auth/me')
    return User
}


export const IsAuthenticated = async ({context}) => {
    try {
        const {queryClient,AuthStore} = context 
    const Auth = AuthStore.getState().auth

    const User = queryClient.ensureQueryData({
        queryKey:['current-user'],
        queryFn:GetUser
    })

    return true; 
    } catch (error) {
        return redirect({to:'/auth'})
    }

}